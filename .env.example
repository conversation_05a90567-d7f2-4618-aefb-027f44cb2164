# -----------------------------------------------------------------------------
# App - Don't add "/" in the end of the url (same in production)
# -----------------------------------------------------------------------------
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_APP_NAME=WR.DO

# -----------------------------------------------------------------------------
# Database
# -----------------------------------------------------------------------------
DATABASE_URL='postgres://[user]:[password]@[hostname]:5432/[dbname]'

# -----------------------------------------------------------------------------
# Authentication (NextAuth.js 5.0.x)
# -----------------------------------------------------------------------------
AUTH_SECRET=abc123
AUTH_URL=http://localhost:3000

GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
GITHUB_ID=
GITHUB_SECRET=
LinuxDo_CLIENT_ID=
LinuxDo_CLIENT_SECRET=

# -----------------------------------------------------------------------------
# Email api (https://resend.com) for Auth login (NextAuth) and send email
# -----------------------------------------------------------------------------
RESEND_API_KEY=
RESEND_FROM_EMAIL="wrdo <<EMAIL>>"

# Google Analytics
NEXT_PUBLIC_GOOGLE_ID=
# Umami Script
NEXT_PUBLIC_UMAMI_SCRIPT=
NEXT_PUBLIC_UMAMI_WEBSITE_ID=

# ScreenShot API
SCREENSHOTONE_BASE_URL=https://shot.wr.do

# GitHub api token for getting gitHub stars count
GITHUB_TOKEN=

# Skip DB check and migration. if false, will check and migrate database each time start docker compose.
SKIP_DB_CHECK=false
SKIP_DB_MIGRATION=false

# Support email
NEXT_PUBLIC_SUPPORT_EMAIL=<EMAIL>