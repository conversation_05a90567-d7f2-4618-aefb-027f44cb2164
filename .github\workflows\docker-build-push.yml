name: <PERSON>uild and Push Docker Image to GHCR

on:
  push:
    branches:
      - main
      - s3/cloudflare-r2
    tags:
      - "v*.*.*"
  pull_request:
    branches:
      - main

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}/wrdo

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
      # 检出代码
      - name: Checkout repository
        uses: actions/checkout@v4

      # 设置 Docker Buildx（支持多平台构建）
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      # 登录到 GitHub Container Registry
      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      # 提取 Docker 镜像元数据（标签、版本等）
      - name: Extract Docker metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=sha,format=short  
            type=ref,event=branch,prefix=  
            type=ref,event=tag

      # 构建并推送 Docker 镜像
      - name: Build and push Docker image
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./Dockerfile
          push: true # ${{ github.event_name != 'pull_request' }}
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          build-args: |
            ENVIRONMENT=${{ github.event.inputs.environment || 'production' }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
