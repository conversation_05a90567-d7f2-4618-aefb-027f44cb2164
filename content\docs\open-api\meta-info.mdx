---
title: Meta Scraping API
description: Extract website meta information
---

##  Usage

API URL:
```bash
https://wr.do/api/v1/scraping/meta?url=https://www.example.com&key=your_api_key
```

API Response:
```json
{
  title: "",
  description: "",
  image: "",
  icon: "",
  url: "",
  lang: "",
  author: "",
  timestamp: "",
  payload: "",
}
```

Example:

[https://wr.do/api/v1/scraping/meta?url=https://vmail.dev&key=example_key](https://wr.do/api/v1/scraping/meta?url=https://vmail.dev&key=b6e1c218-c090-434d-ba3f-97fb48fbccd9)
