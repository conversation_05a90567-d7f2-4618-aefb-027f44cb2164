{"name": "wr.do", "version": "1.1.3", "author": {"name": "oiov", "url": "https://github.com/oiov"}, "scripts": {"dev": "next dev", "build": "next build", "turbo": "next dev --turbo", "start": "next start", "start-docker": "npm-run-all check-db start-server", "start-server": "node server.js", "lint": "next lint", "preview": "next build && next start", "postinstall": "prisma generate", "db:push": "npx prisma migrate deploy", "email": "email dev --dir emails --port 3333", "remove-content": "node ./setup.mjs", "check-db": "node scripts/check-db.js"}, "prisma": {"schema": "./prisma/schema.prisma"}, "dependencies": {"@auth/prisma-adapter": "^2.4.1", "@aws-sdk/client-s3": "^3.840.0", "@aws-sdk/s3-request-presigner": "^3.840.0", "@hookform/resolvers": "^3.9.0", "@mantine/hooks": "^8.0.1", "@prisma/client": "^5.17.0", "@radix-ui/react-accessible-icon": "^1.1.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@react-email/button": "0.0.15", "@react-email/components": "0.0.21", "@react-email/html": "0.0.8", "@scaleway/random-name": "^5.1.1", "@t3-oss/env-nextjs": "^0.11.0", "@types/d3-scale": "^4.0.9", "@types/d3-scale-chromatic": "^3.1.0", "@types/lodash-es": "^4.17.12", "@types/ms": "^2.1.0", "@types/next-pwa": "^5.6.9", "@types/three": "^0.176.0", "@typescript-eslint/parser": "^7.16.1", "@uiw/react-json-view": "2.0.0-alpha.26", "@unovis/react": "^1.4.3", "@unovis/ts": "^1.4.3", "@vercel/analytics": "^1.3.1", "@vercel/functions": "^1.4.0", "@vercel/og": "^0.6.2", "chalk": "^4.1.1", "cheerio": "1.0.0-rc.12", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "concurrently": "^8.2.2", "contentlayer2": "^0.5.0", "crypto": "^1.0.1", "d3-scale": "^4.0.2", "d3-scale-chromatic": "^3.1.0", "date-fns": "^3.6.0", "dotenv": "^10.0.0", "framer-motion": "^12.5.0", "globe.gl": "^2.41.4", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "lucide-react": "^0.414.0", "lucide-static": "^0.460.0", "minimist": "^1.2.8", "ms": "^2.1.3", "next": "14.2.28", "next-auth": "5.0.0-beta.18", "next-contentlayer2": "^0.5.0", "next-intl": "^4.1.0", "next-pwa": "^5.6.0", "next-themes": "^0.3.0", "next-view-transitions": "^0.3.0", "nodemailer": "^6.9.14", "npm-run-all": "^4.1.5", "peerjs": "^1.5.4", "prop-types": "^15.8.1", "react": "18.3.1", "react-colorful": "^5.6.1", "react-country-flag": "^3.1.0", "react-countup": "^6.5.3", "react-day-picker": "^8.10.1", "react-dom": "18.3.1", "react-dropzone": "^14.3.8", "react-email": "2.1.5", "react-globe.gl": "^2.33.2", "react-hook-form": "^7.52.1", "react-quill": "^2.0.0", "react-textarea-autosize": "^8.5.3", "recharts": "^2.12.7", "resend": "^3.4.0", "semver": "^7.5.4", "sharp": "^0.33.4", "shiki": "^1.11.0", "sonner": "^1.5.0", "swr": "^2.2.5", "tailwind-merge": "^2.4.0", "tailwindcss-animate": "^1.0.7", "three": "^0.176.0", "timeago-react": "^3.0.7", "turndown": "^7.2.0", "ua-parser-js": "^1.0.38", "vaul": "^0.9.1", "zod": "^3.23.8"}, "devDependencies": {"@commitlint/cli": "^19.3.0", "@commitlint/config-conventional": "^19.2.2", "@ianvs/prettier-plugin-sort-imports": "^4.3.1", "@tailwindcss/line-clamp": "^0.4.4", "@tailwindcss/typography": "^0.5.13", "@types/lodash": "^4.17.16", "@types/node": "^20.14.11", "@types/qrcode": "^1.5.5", "@types/react": "18.3.3", "@types/react-dom": "18.3.0", "@types/turndown": "^5.0.5", "@types/ua-parser-js": "^0.7.39", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-config-next": "14.2.5", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.35.0", "eslint-plugin-tailwindcss": "^3.17.4", "husky": "^9.1.1", "mdast-util-toc": "^7.1.0", "postcss": "^8.4.39", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.5", "pretty-quick": "^4.0.0", "prisma": "^5.17.0", "qrcode": "^1.5.4", "rehype": "^13.0.1", "rehype-autolink-headings": "^7.1.0", "rehype-pretty-code": "^0.13.2", "rehype-slug": "^6.0.0", "remark": "^15.0.1", "remark-gfm": "^4.0.0", "tailwindcss": "^3.4.6", "typescript": "5.5.3", "unist-util-visit": "^5.0.0"}}