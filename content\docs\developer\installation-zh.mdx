---
title: 开发手册
description: 简单介绍 WR.DO 部署所需的环境变量
---

<DocsLang en="/docs/developer/installation" zh="/docs/developer/installation-zh" />

<Callout type="info" twClass="mt-4">
  建议详细阅读以下官方部署文档：
    - [快速开始](/docs/developer/quick-start-zh) 获取各项配置
    - [部署指南](/docs/developer/deploy-zh) 了解部署方法

  或参考社区优秀部署文档：
    - https://linux.do/t/topic/711806 
    - https://bravexist.cn/2025/06/wr.do.html
    - https://b23.tv/fWpMFQu (视频教程)
</Callout>

<Steps>

### 创建项目

首先使用 `create-next-app` 创建一个新的 Next.js 项目：

```bash
npx create-next-app wrdo --example "https://github.com/oiov/wr.do"
```

或者使用 Vercel 部署：

[![使用 Vercel 部署](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2Foiov%2Fwr.do)

<Callout type="warning" twClass="mt-4">
  这是一种创建代码仓库的好方法，但是部署可能会失败，
  因为你需要在本地项目中添加环境变量。请按照文档进行设置。
</Callout>

### 安装依赖

进入文件夹并为项目安装依赖项：

```bash
cd wrdo
pnpm install
```

### 创建 `.env` 文件

将 `.env.example` 内容复制粘贴到 `.env` 文件中：

| 环境变量                       | 值                                                 | 描述                                                                       |
| -------------------------- | ------------------------------------------------- | ------------------------------------------------------------------------ |
| NEXT_PUBLIC_APP_URL              | `http://localhost:3000`                           | Next.js 应用的 URL。                                                         |
| NEXT_PUBLIC_APP_NAME              | `wr.do`                                           | 网站名称。                                                             |
| AUTH\_SECRET               | `123465`                                          | 用于加密令牌和邮件验证哈希的密钥。                                                        |
| AUTH_URL | `http://localhost:3000`                           | NextAuth.js 的回调 URL。                                                         |
| DATABASE\_URL              | `postgres://username:password@host:port/database` | Postgres 数据库的路径。                                                         |
| GOOGLE\_CLIENT\_ID         | `123465`                                          | Google OAuth 客户端的 ID。                                                    |
| GOOGLE\_CLIENT\_SECRET     | `123465`                                          | Google OAuth 客户端的密钥。                                                     |
| GITHUB\_ID                 | `123465`                                          | GitHub OAuth 客户端的 ID。                                                    |
| GITHUB\_SECRET             | `123465`                                          | GitHub OAuth 客户端的密钥。                                                     |
| RESEND\_API\_KEY           | `123465`                                          | Resend 的 API 密钥。                                                         |
| RESEND\_FROM\_EMAIL        | `"you <<EMAIL>>"`                 | 用于发送邮件的邮箱地址。                                                             |
| SCREENSHOTONE\_BASE\_URL   | `https://api.example.com`                         | 待补充                                                                      |
| GITHUB\_TOKEN              | `ghp_sscsfarwetqet`                               | [https://github.com/settings/tokens](https://github.com/settings/tokens) |
| NEXT_PUBLIC_GOOGLE_ID | `G-EWREW323` | Google Analytics ID |
| NEXT_PUBLIC_UMAMI_SCRIPT | `https://umami.example.com/script.js` | Umami 站点脚本 |
| NEXT_PUBLIC_UMAMI_WEBSITE_ID | `123465` | Umami 站点 ID |
| NEXT_PUBLIC_SUPPORT_EMAIL | `<EMAIL>` | '联系站长'邮箱 |
| SKIP_DB_CHECK | `false` | 跳过数据库连接检测, 建议设置为 `false` |
| SKIP_DB_MIGRATION | `false` | 跳过数据库表更新, 建议设置为 `false` |

如需逐步安装说明，请参见 [快速开始](/docs/developer/quick-start)。

* 如何获取 `GOOGLE_CLIENT_ID`、`GITHUB_ID`，请参见 [认证](/docs/developer/authentification)。
* 如何获取 `RESEND_API_KEY`，请参见 [邮件](/docs/developer/email)。
* 如何启用邮件 worker，请参见 [邮件 Worker](/docs/developer/cloudflare-email-worker)。

### 配置部分

在使用 `pnpm run dev` 之前，请确保检查配置部分并更新所有环境变量。

</Steps>
