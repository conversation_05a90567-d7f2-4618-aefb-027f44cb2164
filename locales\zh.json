{"Common": {"Set up an administrator": "设置管理员", "Add the first domain": "添加第一个域名", "Congrats on completing setup 🎉": "恭喜完成初始化配置 🎉", "Admin Setup Guide": "初始化引导", "Previous": "上一步", "Next": "下一步", "🚀 Start": "🚀 开始", "Ready": "已开启", "Allow Sign Up": "允许注册", "Set {email} as ADMIN": "设置 {email} 为管理员", "Active Now": "立即激活", "Only by becoming an administrator can one access the admin panel and add domain names": "只有成为管理员后才能访问管理员面板并添加域名", "Administrators can set all user permissions, allocate quotas, view and edit all resources (short links, subdomains, email), etc": "管理员可以设置所有用户权限,分配配额,查看和编辑所有资源(短链,子域名,邮件)", "Via": "查看", "quick start": "快速开始", "docs to get more information": "文档获取更多信息", "Domain Name": "域名", "Please enter a valid domain name (must be hosted on Cloudflare)": "请输入有效的域名(确保已经托管到 Cloudflare)", "Or add later": "或稍后添加", "Submit": "提交", "After v1-0-2, this setup guide is not needed anymore": "此初始化引导在 v1.0.2 版本后, 不再是必要步骤"}, "List": {"Short URLs": "短链列表", "Manage Short URLs": "管理短链", "Add URL": "创建短链", "Slug": "短链", "Target": "目标", "User": "用户", "Enabled": "启用", "Expiration": "有效期", "Clicks": "点击量", "Updated": "更新", "Actions": "操作", "Edit": "编辑", "Search by slug": "搜索短链", "Search by target": "搜索目标链接", "Search by username": "搜索用户名", "Create short link": "创建短链", "Edit short link": "编辑短链", "Delete": "删除", "Target URL": "目标链接", "Required": "必填", "Short Link": "短链", "A random url suffix": "短链后缀", "Final url like": "最终链接为", "Password": "访问密码", "Optional": "可选", "If you want to protect your link": "如果您想保护您的链接", "Enter 6 character password": "请输入6位密码", "Expiration time, default for never": "过期时间，默认为永不过期", "Save": "保存", "Cancel": "取消", "Update": "更新", "QR Code Design": "自定义二维码", "Preview": "预览", "Download SVG": "下载 SVG 图片", "Download PNG": "下载 PNG 图片", "Download JPG": "下载 JPG 图片", "Url": "链接", "Logo": "图标", "Custom Logo": "自定义图标", "Front Color": "前景色", "Background Color": "背景色", "Display your logo in the center of the QR code": "在二维码中央显示您的图标", "Learn more": "了解更多", "Customize your QR code logo": "自定义二维码图标", "Please create a api key before use this feature": "请在使用此功能之前创建一个 API 密钥", "Learn more about": "了解更多关于", "Create Api Key": "创建 API 密钥", "Show": "显示", "per page": "条/页", "Total Subdomains": "总计", "Subdomain List": "子域名列表", "Before using please read the": "在使用之前请阅读", "legitimacy review": "链接合法性审查", "See": "查看", "examples": "示例", "for more usage": "了解更多用法", "Add Record": "添加记录", "Type": "类型", "Name": "名称", "Content": "内容", "TTL": "TTL", "Status": "状态", "Pending": "审核中", "The record is currently pending for admin approval": "正在等待管理员审核", "The target is currently inaccessible": "目标链接目前无法访问", "Please check the target and try again": "请检查解析记录并重试", "If the target is not activated within 3 days": "如果目标链接在 3 天后依然无法访问", "the administrator will": "管理员将", "delete this record": "删除此记录", "Review": "审核", "No Subdomains": "暂无子域名", "No urls": "暂无短链接", "Create record": "创建记录", "Edit record": "编辑记录", "The administrator has enabled application mode": "管理员已启用 [用户申请 - 管理员审核] 模式", "After submission, you need to wait for administrator approval before the record takes effect": "提交后, 您需要等待管理员审核才能生效", "What are you planning to use the subdomain for?": "您计划使用此域名做什么？", "At least 20 characters, Max 100 characters": "至少 20 个字符，最多 100 个字符", "User email": "用户邮箱", "Domain": "根域名", "No domains configured": "未配置域名", "Select a domain": "选择一个域名", "IPv4 address": "IPv4 地址", "Example": "例如", "Time To Live": "生效时间", "Proxy": "代理记录", "Proxy status": "DNS 响应被 Cloudflare Anycast IP 替代", "Total Domains": "域名管理", "Add Domain": "添加域名", "Domain Name": "域名", "Shorten Service": "短链服务", "Email Service": "邮件服务", "Subdomain Service": "子域名服务", "Active": "启用", "Search by domain name": "搜索域名", "No Domains": "暂无域名", "Verified": "已就绪", "Verify Configuration": "验证配置", "Create Domain": "创建域名", "Edit Domain": "编辑域名", "Base": "基础配置", "Services": "服务配置", "Cloudflare Configs": "Cloudflare 配置", "Zone ID": "Zone ID", "API Token": "API Token", "Account Email": "账户邮箱", "How to get zone id?": "如何获取 Zone ID?", "How to get api key?": "如何获取 API 密钥?", "How to get cloudflare account email?": "如何获取账户邮箱?", "Resend Configs": "Resend 配置", "Associate with 'Subdomain Service' status": "与 '子域名服务' 启用状态关联", "Associate with 'Email Service' status": "与 '邮件服务' 启用状态关联", "API Key": "API 密钥", "send email service": "用于发送邮件服务", "How to get resend api key?": "如何获取 Resend API 密钥?", "Analytics": "访客分析", "Edit URL": "编辑短链", "Plan Name": "计划名称", "Quota Settings": "配额设置", "Short Limit": "创建短链数量", "Record Limit": "创建子域名数量", "Email Limit": "创建邮箱数量", "Send Limit": "发件数量", "Domain Limit": "域名限制", "Add Plan": "添加计划", "No Plans": "暂无计划", "Create Plan": "创建计划", "Edit Plan": "编辑计划", "Monthly limit of short links created": "每月新创建短链数量限制", "Monthly limit of subdomains created": "每月新创建子域名数量限制", "Monthly limit of emails sent": "每月发送邮件数量限制", "Monthly limit of email addresses created": "每月新创建邮箱地址数量限制", "Reject": "拒绝", "Rejected": "已拒绝", "View Period": "时间范围", "Time range for viewing short link visitor statistics data (days)": "查看短链访问量统计数据的时间范围(天)", "Tracked Limit": "跟踪统计限制", "Monthly limit of tracked clicks (times)": "每月最大跟踪点击量限制(次)", "Limit on the number of allowed domains": "允许的域名数量限制", "Only active plans can be used": "只有启用的计划才能生效", "Plan name must be unique": "计划名称必须唯一", "Record Types": "DNS 记录类型", "Allowed record types": "请填写标准的 DNS 记录类型", "use `,` to separate": "使用 `,` 分隔", "Agree": "同意", "Min URL Length": "短链后缀最短长度", "Min Email Length": "邮箱前缀最短长度", "Min Subdomain Length": "子域名前缀最短长度", "Limit Configs": "域名限制", "Email": "邮箱", "Role": "角色", "Plan": "计划", "Join": "注册", "ADMIN": "管理员", "Admin": "管理员", "USER": "用户", "Total Users": "用户总数", "Edit User": "编辑用户", "Login Password": "用户密码", "Duplicate": "复制", "Confirm duplicate domain": "确认复制域名", "This will duplicate all configuration information for the {domain} domain, and create a new domain": "这将复制 {domain} 域名的所有配置信息，并创建一个新域名", "Add User": "添加用户", "Loading storage buckets": "正在加载存储桶", "No buckets found": "未配置存储桶", "The administrator has not configured the storage bucket, no file can be uploaded": "管理员未配置存储桶，无法上传文件", "No Files": "暂无文件", "You don't upload any files yet": "您还没有上传任何文件", "Size": "大小", "Date": "日期", "Download": "下载文件", "Generate short link": "生成短链", "Delete File": "删除文件", "Select all": "全部选中", "Delete selected": "删除选中", "Update short link": "更新短链", "QR Code": "二维码", "Raw Data": "在线预览", "Storage Service": "存储服务", "Max File Size": "单文件大小上限", "Maximum uploaded single file size in bytes": "单个文件最大上传大小（字节）", "Max Total Size": "总文件大小上限", "Maximum uploaded total file size in bytes": "所有文件最大上传总大小（字节）", "storageUsage": "存储空间使用情况", "used": "已使用", "usedSpace": "已使用空间", "totalCapacity": "总容量", "availableSpace": "剩余空间", "storageFull": "存储空间即将用完", "storageHigh": "存储空间使用较多", "storageGood": "存储空间充足", "items": "条", "Total": "共", "Configuration Error": "配置错误"}, "Components": {"Dashboard": "用户面板", "Live Logs": "实时日志", "Real-time logs of short link visits": "展示短链实时访问日志", "Stop": "停止", "Live": "实时", "Time": "时间戳", "Slug": "短链", "Target": "目标", "Location": "位置", "Clicks": "点击量", "of": "/", "total logs": "条日志", "API Reference": "API 使用说明", "provide a api for {target}": "为 {target} 提供了 API", "creating short urls": "创建短链", "View the usage tutorial": "查看使用教程", "document": "文档", "Realtime Visits": "访问量", "See documentation": "查看相关文档", "Manage Short URLs": "管理短链", "List and manage short urls": "展示短链接列表并管理你的短链", "Manage DNS Records": "管理 DNS 记录", "List and manage records": "展示 DNS 记录列表并管理你的 DNS 记录", "Scraping API Overview": "Scraping API 概览", "Quickly extract valuable structured website data": "快速提取有价值的网站数据", "Url to Screenshot": "网址转截图", "Quickly extract website screenshots": "快速提取网站截图", "extracting url as screenshot": "提取 URL 为截图", "Url to QR Code": "网址转二维码", "Generate QR Code from URL": "从 URL 生成二维码", "extracting url as QR code": "提取 URL 为二维码", "Url to Meta Info": "网站元数据提取", "extracting url as meta info": "提取 URL 网址中的元数据", "Url to Markdown": "网页内容转 Markdown", "Quickly extract website content and convert it to Markdown format": "快速提取网站内容并将其转换为 Markdown 格式", "extracting url as markdown": "提取 URL 为 Markdown", "extracting url as text": "提取 URL 为文本", "Account Settings": "账户设置", "Manage account and website settings": "管理账户信息和网站设置", "Setup Guide": "初始化引导", "Admin Panel": "管理员面板", "Access only for users with ADMIN role": "仅管理员可访问", "Domains Management": "域名管理", "List and manage domains": "展示域名列表并管理你的域名服务", "User Management": "用户管理", "List and manage all users": "展示用户列表并管理所有用户", "Email box": "邮件箱", "monthly": "每月", "total": "总计", "Short URLs": "短链接", "DNS Records": "DNS 记录", "Url to Text": "网址转文本", "Take a screenshot of the webpage": "使用 API 提取网页的截图", "Extract website metadata": "使用 API 提取网页的元数据", "Convert website content to Markdown format": "使用 API 将网页内容转换为 Markdown 格式", "Convert website content to text": "使用 API 将网页内容转换为文本", "Emails": "邮箱", "Inbox": "收件箱", "Users": "用户", "Total Requests of APIs in Last 30 Days": "过去 30 天的 API 请求总量", "Last request from {latestFrom} api about {latestDate}": "最近的请求来自 {latestFrom} 的 API 于 {latestDate}", "last-request-info": "最近的请求来自 {location} 于 <timeAgo></timeAgo>", "Requests": "请求量", "IP": "IP 数", "Date": "日期", "Type": "类型", "Link": "链接", "User": "用户", "Data Increase": "数据增长", "Showing data increase in": "Showing data increase in", "Records": "子域名", "URLs": "短链接", "Sends": "发件箱", "Link Analytics": "访客分析", "Last visitor from {latestFrom} about {latestDate}": "最近的访客来自 {latestFrom} 于 {latestDate}", "last-visitor-info": "最后访问者来自 {location} 于 <timeAgo></timeAgo>", "Views": "浏览量", "Visits": "访问次数", "Referrers": "来源域名", "Traffic Type": "流量类型", "Country": "国家", "City": "城市", "Browser": "浏览器", "Engine": "引擎", "Language": "语言", "Region": "地区", "Device": "设备", "OS": "操作系统", "CPU": "CPU", "Visitors": "访客", "Name": "名称", "Activated Api Key users": "已启用 API Key 的用户", "total-requests-one-type": "Open API 的 {type1} 的总请求数", "total-requests-two-types": "Open API 的 {type1} 和 {type2} 的总请求数", "Protected Link": "受保护的链接", "You are attempting to access a password-protected link": "您正在尝试访问受密码保护的链接", "Please contact the owner to get the password": "请联系链接所有者获取密码", "Learn more about this from our": "了解更多请参考", "docs": "文档", "Incorrect password": "密码错误", "Please try again": "请重试", "Unlock": "解锁", "Unlocking": "解锁中...", "Last 24 Hours": "最近 24 小时", "Last 7 Days": "最近 7 天", "Last 30 Days": "最近 30 天", "Last 2 Months": "最近 2 个月", "Last 3 Months": "最近 3 个月", "Last 6 Months": "最近 6 个月", "Last 1 Year": "最近 1 年", "All the time": "所有时间", "No Visits": "无访问记录", "You don't have any visits yet in": "您还没有任何访问记录于", "System Settings": "系统设置", "Active": "有效解析", "Inactive": "无效解析", "Pending": "审核中", "Rejected": "已拒绝", "linkNotExist": "链接不存在", "linkNotExistDescription": "很抱歉，您访问的短链接不存在。可能已被删除或从未创建过。", "linkExpired": "链接已过期", "linkExpiredDescription": "这个短链接已经过期，无法继续使用。", "linkDisabled": "链接已禁用", "linkDisabledDescription": "这个短链接已被创建者禁用。", "systemError": "系统错误", "systemErrorDescription": "处理您的请求时发生了错误，请稍后重试。", "contactCreatorReactivate": "请联系短链接创建者重新激活或创建新的短链接。", "contactCreatorOrAdmin": "请联系短链接创建者或系统管理员。", "shortLink": "短链接", "retry": "重试", "backToHome": "返回首页", "goBack": "返回上页", "contactSupportIfError": "如果您认为这是一个错误，请联系技术支持", "Actived": "有效", "Disabled": "已禁用", "Expired": "已过期", "PasswordProtected": "密码保护", "Upload List": "上传列表", "Uploading": "上传中", "Completed": "已完成", "Aborted": "已中止", "Drop files to upload them to": "将文件上传到", "Drag and drop file(s) here": "将文件拖到此处上传", "or": "或", "Browse file(s)": "浏览本地文件", "Cloud Storage": "云存储", "List and manage cloud storage": "上传和管理云存储文件", "Cancel": "取消", "Clear": "清空", "Upload Files": "上传文件", "Uploud channel": "渠道", "Do not close the window until the upload is complete": "在上传完成之前不要浏览器关闭窗口", "Pending Upload": "等待上传", "Start Upload": "开始上传", "Limit": "限制", "Files pasted successfully": "文件粘贴成功"}, "Landing": {"settings": "设置", "Dashboard": "控制面板", "deployWithVercel": "使用", "now": "部署私有版本", "onePlatformPowers": " ", "endlessSolutions": "一站式域名服务平台", "platformDescription": "集成短链生成、子域名托管、无限邮箱服务，以及开放API接口，一站式域名管理解决方案，释放你的域名潜力", "documents": "参考文档", "signInForFree": "免费登录", "exampleImageAlt": "示例", "urlShorteningTitle": "短链生成器", "urlShorteningDescription": "📖 立即将冗长、复杂的URL转换为短小易记的链接，便于分享。享受内置的分析功能，实时跟踪点击量、监控性能并深入了解您的受众。支持设置密码保护、自定义二维码生成，支持调用API生成。", "freeSubdomainHostingTitle": "子域名托管", "freeSubdomainHostingDescription": "🎉 管理多 Cloudflare 账户下的多个域名的 DNS 记录，支持创建多种 DNS 记录类型（CNAME、A、TXT 等）。", "emailReceiversSendersTitle": "电子邮件接收与发送", "emailReceiversSendersDescription": "📧 创建无限个自定义前缀邮箱，接收无限制邮件，支持调用 API 创建邮箱、调用 API 获取收件箱邮件，支持过滤未读邮件列表。", "multipleDomainsTitle": "多域名支持", "multipleDomainsDescription": "🤩 通过多个域名的灵活性增强您的业务，例如wr.do、uv.do等。建立强大的数字足迹，创建品牌链接，或在一个统一平台下管理多样化项目。", "websiteScreenshotApiTitle": "网站截图API", "websiteScreenshotApiDescription": "📷 使用我们强大的截图API即时捕获任何网页的高质量截图。无缝集成到您的应用程序中，访问第三方服务，并通过申请您的唯一API密钥解锁功能。", "metaInformationApiTitle": "元信息API", "metaInformationApiDescription": "🍥 使用我们智能的元信息API轻松提取丰富、结构化的网页数据。适合开发者、商家或研究人员，此工具提供无缝集成、第三方服务访问和增强功能。", "applyYourApiKey": "申请您的API密钥", "pricingTitle": "定价", "pricingDescription": "免费为个人使用，当您的团队需要高级控制时可升级。", "freeTier": "免费体验", "freePrice": "$0/mo", "freeBestFor": "适合爱好者和需要管理链接的个人", "getStartedFree": "免费开始", "premiumTier": "高级", "premiumPrice": "$5/mo", "premiumBestFor": "最适合5-50人的团队", "getFreeTrial": "获取免费试用", "enterpriseTier": "企业计划", "enterprisePrice": "联系我们", "enterpriseBestFor": "适合有定制需求的大型组织", "contactUs": "联系我们"}, "Auth": {"Back": "返回", "Welcome to": "欢迎使用", "Choose your login method to continue": "选择登录方式以继续", "By clicking continue, you agree to our": "点击继续即表示您同意我们的", "Terms of Service": "服务条款", "and": "和", "Privacy Policy": "隐私政策", "Or continue with": "或使用", "Email": "邮箱", "Sign Up with Email": "使用邮箱注册", "Sign In with Email": "使用邮箱登录", "Email Code": "邮箱验证", "Password": "账号密码", "Sign In / Sign Up": "点击登录/注册", "Incorrect email or password": "邮箱或密码错误，或管理员关闭了新用户注册", "Something went wrong": "出错了", "Check your email": "检查您的邮箱", "We sent you a login link": "我们已向您发送登录链接", "Be sure to check your spam too": "请确保检查您的垃圾邮件", "Welcome back!": "欢迎回来!", "Unregistered users will automatically create an account": "未注册用户将自动创建账户", "Administrator has disabled new user registration": "管理员已关闭新用户注册", "Email domain not supported, Please use one of the following:": "暂不支持此邮箱后缀，请使用以下邮箱:", "Auth configuration error": "权限校验配置错误", "Unknown error": "未知错误"}, "System": {"MENU": "菜单", "Dashboard": "控制台", "Short Urls": "短链接", "Emails": "邮件箱", "DNS Records": "子域名", "WRoom": "聊天室", "OPEN API": "开放API", "Overview": "概览面板", "Screenshot": "截图API", "QR Code": "二维码API", "Meta Info": "元数据API", "Markdown": "<PERSON><PERSON>", "ADMIN": "管理员", "Admin Panel": "概览面板", "Domains": "域名管理", "Users": "用户管理", "URLs": "短链管理", "Records": "子域名管理", "OPTIONS": "选项", "Settings": "账户设置", "Documentation": "使用文档", "Feedback": "反馈", "Support": "联系站长", "Docs": "文档", "Toggle theme": "主题切换", "Light": "浅色", "Dark": "深色", "System": "跟随系统", "Admin": "管理面板", "Sign in": "登录", "Log out": "退出登录", "System Settings": "系统设置", "Cloud Storage": "云存储", "Cloud Storage Manage": "云存储管理"}, "Email": {"Search emails": "搜索邮箱...", "Create New Email": "创建新邮箱", "Email Address": "邮箱地址", "Inbox Emails": "收件箱", "Unread Emails": "未读邮件", "Filter unread emails": "过滤未读邮件", "Sent Emails": "已发送", "Admin Mode": "管理员模式", "No emails": "暂无邮件", "{email} recived": "{email} 封邮件", "Edit email": "编辑邮箱", "Create new email": "创建新邮箱", "Enter email prefix": "请输入邮箱前缀", "No domains configured": "未配置域名", "Cancel": "取消", "Update": "更新", "Create": "创建", "Failed to load emails": "加载邮件失败", "Please try again": "请重试", "No emails found": "未找到邮件", "Search by send to email": "搜索收件人邮箱...", "Delete email": "删除邮箱", "You are about to delete the following email, once deleted, it cannot be recovered": "您即将删除此邮件，一旦删除，无法恢复", "All emails in inbox will be deleted at the same time": "所有收件箱中的邮件将同时删除", "Are you sure you want to continue?": "确定要继续吗?", "To confirm, please type": "若确认请在输入框输入", "delete": "删除", "below": "下面", "Confirm Delete": "确认删除", "INBOX": "收件箱", "Auto refresh": "自动刷新", "more": "更多", "Select all": "全部选中", "Mask as read": "标记为已读", "Delete selected": "删除选中", "Waiting for emails": "等待接收邮件", "No Email Address Selected": "暂未选择邮箱地址", "Please select an email address from the list to view your inbox": "请从列表中选择一个邮箱地址以查看收件箱", "Once selected, your emails will appear here automatically": "选择后，您的邮件将自动显示在这里", "How to use email to send or receive emails?": "如何使用邮箱发送或接收邮件？", "Will my email or inbox expire?": "我的邮箱或收件箱是否会过期？", "What is the limit? It's free?": "邮箱使用有限制吗？是否免费？", "How to create emails with api?": "如何使用 API 创建邮箱地址？", "Send Email": "发送邮件", "From": "发件人", "To": "收件人", "Subject": "主题", "Content": "内容", "Send": "发送", "Sending": "发送中...", "Reply-To": "回复", "Attachments": "附件", "Date": "日期"}, "Scrape": {"Playground": "在线体验", "Automate your website screenshots and turn them into stunning visuals for your applications": "自动化截图并转换成多种格式图片", "Start": "开始", "Scraping": "处理中...", "Scrape the meta data of a website": "快速从网站中抓取元数据", "Text": "文本"}, "Setting": {"Name": "昵称", "Your Name": "您的昵称", "Save": "保存", "Please enter a display name you are comfortable with": "请输入您的昵称，未设置则为匿名", "Max 32 characters": "最多32个字符", "Your Role": "您的角色", "Role": "角色", "ADMIN": "系统管理员", "USER": "普通用户", "Select the role what you want for this app": "设置您的权限角色", "API Key": "API 密钥", "Generate": "生成", "Generate a new API key to access the open apis": "生成你的专属 API 密钥", "Delete Account": "删除账户", "This is a danger zone - Be careful !": "这是一个危险操作！", "Are you sure": "确定删除?", "Active Subscription": "包含订阅", "Permanently delete your {name} account": "永久删除您的 {name} 账户", " and your subscription": "和您的订阅", "This action cannot be undone - please proceed with caution": "此操作不可逆，请谨慎操作", "Warning": "警告", "This will permanently delete your account and your active subscription!": "这将永久删除您的账户和订阅!", "verification": "为了验证，请在下方输入 <confirm>确认删除账户</confirm>", "App Configs": "应用配置", "User Registration": "用户注册", "Allow users to sign up": "是否允许用户注册", "Subdomain Apply Mode": "子域名申请模式", "Enable subdomain apply mode, each submission requires administrator review": "启用子域名申请模式，用户每次提交新子域名需要管理员审核", "Notification": "系统通知", "Set system notification, this will be displayed in the header": "设置系统通知，将在网页顶部显示", "Login Methods": "登录方式", "Select the login methods that users can use to log in": "选择用户可以使用的登录方式", "Resend Email": "Resend 邮箱登录", "Email Password": "账号密码登录", "Your Password": "账号密码", "Update your password": "更新您的密码", "At least 6 characters, Max 32 characters": "密码长度至少6位，最多32位", "Subdomain Configs": "子域配置", "Email Configs": "电子邮件配置", "Enable email catch-all, all user's email address which created on this platform will be redirected to the catch-all email address": "启用 Catch-All，所有用户在此平台创建的邮箱所接收的邮件都会被转发到 Catch-All 设置的邮箱地址", "Catch-All Email Address": "Catch-All 邮箱地址", "Set catch-all email address, split by comma if more than one, such as: 1@a-com,2@b-com, Only works when email catch all is enabled": "设置 Catch-All 目标邮箱地址 (仅支持在此平台创建的邮箱)，多个邮箱地址请用逗号分隔，例如：<EMAIL>,<EMAIL>。仅在启用 Catch-All 时生效", "Message Pusher": "消息推送", "Push message to Telegram groups": "推送邮件到 Telegram 频道、群组", "Update now": "立即更新", "Dismiss": "忽略", "New version available": "有新版本可用", "Check for updates": "检查版本更新", "Telegram Pusher": "Telegram 推送", "Telegram Bot Token": "Telegram Bot Token", "Telegram Group ID": "Telegram 群组/频道 ID", "Telegram Message Template": "Telegram 消息模板", "Telegram Push Email White List": "Telegram 推送邮件白名单", "Set Telegram bot token, Only works when Telegram pusher is enabled": "设置 Telegram Bot Token，仅在启用 Telegram 推送时生效", "Set Telegram group ID, split by comma if more than one, such as: -10054275724,-10045343642": "设置 Telegram 群组/频道 ID，多个群组/频道 ID请用逗号分隔，例如：-10054275724,-10045343642", "Set Telegram email message template": "设置 Telegram 邮件消息模板", "Set Telegram push email white list, split by comma, if not set, will push all emails": "设置 Telegram 推送邮件白名单，多个邮箱地址请用逗号分隔，若不设置，将推送所有邮件", "How to configure Telegram bot": "如何配置 Telegram Bot", "Email Suffix Limit": "注册邮箱后缀限制", "Enable eamil suffix limit, only works for resend email login and email password login methods": "开启注册邮箱后缀限制，仅适用于 Resend 邮箱登录和账号密码登录方式", "Need to configure": "需要配置", "Email Suffix White List": "白名单", "Set email suffix white list, split by comma, such as: gmail-com,yahoo-com,hotmail-com": "设置邮箱后缀白名单，多个后缀请用逗号分隔，例如：gmail.com,yahoo.com,hotmail.com", "Application Status Email Notifications": "申请状态邮件通知", "Send email notifications for subdomain application status updates; Notifies administrators when users submit applications and notifies users of approval results; Only available when subdomain application mode is enabled": "开启后，用户申请子域名时将邮件通知管理员审核，审核完成后邮件通知用户结果。此功能仅在子域申请模式开启时有效", "Cloud Storage Configs": "云存储配置", "Verified": "已就绪", "Verify Configuration": "验证配置", "Clear": "清空", "How to get the R2 credentials?": "如何获取 R2 授权配置？", "Endpoint": "S3 端点", "Access Key ID": "访问密钥 ID", "Secret Access Key": "机密访问密钥", "Enabled": "启用", "Bucket": "存储桶", "Bucket Name": "存储桶名称", "Public Domain": "公开域名或自定义域名", "Max File Size": "上传文件大小限制", "Region": "存储桶区域", "Prefix": "前缀", "Optional": "可选", "Allowed File Types": "允许的文件类型", "Public": "公开", "Publicize this storage bucket, all registered users can upload files to this storage bucket; If not public, only administrators can upload files to this storage bucket": "公开此存储桶，所有注册用户都可以上传文件到此存储桶; 若不公开，只有管理员可以上传文件到此存储桶", "Provider": "提供渠道", "How to get the S3 credentials?": "如何获取 S3 授权配置？", "Provider Unique Name": "渠道名称", "Unique": "唯一", "Add Provider": "添加渠道", "{length} Buckets": "{length}个存储桶", "Save Modifications": "保存修改"}}