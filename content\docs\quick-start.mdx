---
title: Quick Start Guide
description: Get started with the WR.DO documentation.
---

Welcome to WR.DO, a multi-tenant DNS record management system based on Cloudflare DNS API, also featuring URL shortening capabilities. This guide will help you get started with using our website quickly.

## Registration and Login

1. **Visit the Website**: First, visit https://wr.do .
3. **Register/Log In to Your Account**: Use the registered email or Google/Github account to log in on the [Login page](https://wr.do/login).

## Generating Short Links

1. **Navigate to Short Links Page**: Select the [Short Links](https://wr.do/dashboard/urls) option in the navigation bar.
2. **Input Long URL**: Paste or type the URL you want to shorten into the provided input box.
3. **Generate Short Link**: Click the "Generate" button, and the system will automatically create a short link for you.
4. **Copy and Use**: Click the "Copy" button to copy the short link to your clipboard, and you can use it wherever needed.

## Creating Email Addresses

1. **Navigate to Email Management Page**: After logging in, you will see a "Email Management" option in the navigation bar, click [Email Management](https://wr.do/emails) to proceed.
2. **Add Email**: Click the "Create New Email" button to create a new email address.
3. **Ready to Use**: Once the email address is created, you can start using it for email communication.

## Creating Chat Room 

1. **Navigate to Chat Room Page**: After logging in, you will see a "Chat Room" option in the navigation bar, click [Chat Room](https://wr.do/chat) to proceed.
2. **Share Room**: Click the "Share Room" button to copy the room link and share it with others.
3. **Connect Room**: Once other users join the room, they can start chatting.

## Creating DNS Records

1. **Navigate to DNS Management Page**: After logging in, you will see a "DNS Management" option in the navigation bar, click [DNS Management](https://wr.do/dashboard/records) to proceed.
2. **Add Record**: Click the "Add Record" button and select the type of record you need (such as CNAME, A, etc.).
3. **Fill in Details**: Enter the hostname, target address, and other details as prompted.
4. **Save Record**: After confirming everything is correct, click the "Save" button.

## Real-Time Updates

- **DNS Record Changes**: Once you update or add new DNS records, the system will immediately begin to propagate these changes.
- **Short Link Access**: The generated short link is ready to use instantly, with no additional wait time required.

## Support and Help

If you encounter any issues during use or need further assistance, please visit our [Discord community](https://discord.gg/d68kWCBDEs), where our team and technical community members are happy to provide support.

Thank you for choosing WR.DO, we wish you a pleasant experience!

---

© WR.DO - [MIT License](/LICENSE.md)