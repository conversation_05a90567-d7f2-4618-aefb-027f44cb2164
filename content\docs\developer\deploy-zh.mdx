---
title: 部署指南
description: 选择你的部署方式
---

<DocsLang en="/docs/developer/deploy" zh="/docs/developer/deploy-zh" />

<Callout type="info" twClass="mt-4">
  在阅读此文档之前，建议首先阅读 [快速开始](/docs/developer/quick-start-zh)，以确认准备好依赖的环境、变量。 
</Callout>

## 使用 Vercel 部署（推荐）

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/oiov/wr.do.git&project-name=wrdo)

## 使用 Docker Compose 部署

<Callout type="warning" twClass="mt-4">
  请在部署前先创建你的数据库实例。  
  
  将 `.env` 文件中的 `SKIP_DB_CHECK` 和 `SKIP_DB_MIGRATION` 设置为 `false`，  
  这样会在启动时进行数据库检查、初始化和迁移。
</Callout>

创建一个新文件夹，并将 [docker-compose.yml](https://github.com/oiov/wr.do/blob/main/docker-compose.yml) 和 [.env](https://github.com/oiov/wr.do/blob/main/.env.example) 文件复制到该文件夹中。

> 或者只创建一个 [docker-compose.yml](https://github.com/oiov/wr.do/blob/main/docker-compose.yml) 文件，将其中的 `${DATABASE_URL}` 等变量替换为你的数据库连接地址等信息。

```bash
- wrdo
  | - docker-compose.yml
  | - .env
````

在 `.env` 文件中填写环境变量，然后执行：

```bash
docker compose up -d
```

此命令会自动拉取最新的镜像并启动服务。（自动初始化数据库表，可以在容器日志中查看启动日志）

## 使用 Docker Compose（本地数据库）部署

创建一个新文件夹，并将 `docker-compose-localdb.yml` 和 `.env` 文件复制到该文件夹中。

```bash
- wrdo
  | - docker-compose.yml
  | - .env
```

在 `.env` 文件中填写环境变量，然后执行：

```bash
docker compose up -d
```

## 官方镜像

```bash
docker pull ghcr.io/oiov/wr.do/wrdo:latest
```

在 [container/wr.do](https://github.com/oiov/wr.do/pkgs/container/wr.do%2Fwrdo) 可以找到官方镜像。

## 打包镜像

Fork 此仓库后，在 Actions 中触发打包镜像。

## 付费部署服务

**联系方式：** 微信 `oiovdev`

本项目提供专业的代部署服务，根据不同需求灵活收费。

### 部署方案与价格

| 部署方式 | 配置说明 | 服务费用 |
| --- | --- | --- |
| Vercel | 应用托管 + 自建数据库 + 域名配置 | ¥500 |
| Vercel | 应用托管 + Neon 云数据库 + 域名配置 | ¥400 |
| Docker | 服务器部署 + 自建数据库 + 域名配置 | ¥500 |
| Docker | 服务器部署 + Neon 云数据库 + 域名配置 | ¥450 |

### 重要说明

- **数据库要求：** 自建数据库需要准备服务器
- **域名要求：** 至少准备一个域名，并且需要托管到 Cloudflare
- **服务器与域名费用需另付：** 服务器购买和域名注册费用不包含在上述服务费中，建议您提前自行准备
- **默认环境配置：** 服务器部署默认安装宝塔面板进行管理


> 推荐云服务器 RackNerd，美国免备案vps，2核2G 配置仅需20.98$≈145RMB/年，支持支付宝付款，[💁点击优惠链接直达](https://my.racknerd.com/aff.php?aff=10906&pid=681) 。

> 推荐域名注册商 [NameSilo](https://www.namesilo.com/?rid=50fae21ln), 新用户使用优惠码下单可以减 1$，优惠码: wrdo

### 准备工作

在开始部署前，请提前注册以下平台账户：

- **Cloudflare**：https://dash.cloudflare.com （必须，域名管理服务）
- **Resend**：https://resend.com （必须，邮件发送服务）
- **Vercel**：https://vercel.com （可选，应用部署平台）
- **Neon**：https://neon.tech （可选，云数据库服务）

### 联系方式

如需部署服务，请添加微信 `oiovdev` 详细咨询，将根据您的具体需求提供定制化的部署方案。