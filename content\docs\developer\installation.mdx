---
title: Installation
description: How to install the project.
---

<DocsLang en="/docs/developer/installation" zh="/docs/developer/installation-zh" />

<Callout type="info" twClass="mt-4">
  Suggest reading the following deployment tutorials:
    - [Quick Start](/docs/developer/quick-start) for getting all the configuration
    - [Deploy Guide](/docs/developer/deploy) for deployment methods

  Or read unofficial deployment tutorials:
    - https://linux.do/t/topic/711806 
    - https://bravexist.cn/2025/06/wr.do.html
    - https://b23.tv/fWpMFQu (Video tutorial)
</Callout>

<Steps>

### Create project

Start by creating a new Next.js project using `create-next-app`:

```bash
npx create-next-app wrdo --example "https://github.com/oiov/wr.do"
```

Or deploy with Vercel :

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/oiov/wr.do.git&project-name=wrdo)

<Callout type="warning" twClass="mt-4">
  A good way to create your repository, but the deployment will fail because you
  need to add your environment variables locally in your project. Follow the
  documentation for that.
</Callout>

### Install dependencies

Enter in the folder and install dependencies for your project:

```bash
cd wrdo
pnpm install
```

### Create a `.env` file

Copy/paste the `.env.example` in the `.env` file:

| Environment Variable | Value | Description |
|----------------------|-------|-------------|
| NEXT_PUBLIC_APP_URL | `http://localhost:3000` | The URL of the Next.js application. |
| NEXT_PUBLIC_APP_NAME | `wr.do` | The name of website. |
| AUTH_SECRET | `123465` | The secret used to encrypt tokens and email verification hashes. |
| AUTH_URL | `http://localhost:3000` | NextAuth.js callback URL。   
| DATABASE_URL | `postgres://username:password@host:port/database` | The path to the postgres database. |
| GOOGLE_CLIENT_ID | `123465` | The ID of the Google OAuth client. |
| GOOGLE_CLIENT_SECRET | `123465` | The secret of the Google OAuth client. |
| GITHUB_ID | `123465` | The ID of the GitHub OAuth client. |
| GITHUB_SECRET | `123465` | The secret of the GitHub OAuth client. |
| RESEND_API_KEY | `123465` | The API key for Resend. |
| RESEND_FROM_EMAIL | `"you <<EMAIL>>"` | The email address to send emails from. |
| SCREENSHOTONE_BASE_URL | `https://api.example.com` | The base URL of the screenshotone API. |
| GITHUB_TOKEN | `ghp_sscsfarwetqet` | https://github.com/settings/tokens |
| NEXT_PUBLIC_GOOGLE_ID | `G-EWREW323` | The ID of the Google Analytics. |
| NEXT_PUBLIC_UMAMI_SCRIPT | `https://umami.example.com/script.js` | The URL of the Umami script. |
| NEXT_PUBLIC_UMAMI_WEBSITE_ID | `123465` | The ID of the Umami website. |
| NEXT_PUBLIC_SUPPORT_EMAIL | `<EMAIL>` | The email address for support. |
| SKIP_DB_CHECK | `false` | Skip database check. |
| SKIP_DB_MIGRATION | `false` | Skip database migration. |

- How to get `GOOGLE_CLIENT_ID`、`GITHUB_ID`, see [Authentification](/docs/developer/authentification).
- How to get `RESEND_API_KEY`, see [Email](/docs/developer/email).
- How to active email worker, see [Email Worker](/docs/developer/cloudflare-email-worker).

For step by step installation, see [Quick Start](/docs/developer/quick-start).

### Configuration part

Let's check the configuration part for update all environment variables before use `pnpm run dev`.

</Steps>
