---
title: Telegram 邮件推送功能配置指南
description: How to configure Telegram bot
---

## 功能概述

WR.DO 支持将接收到的邮件自动推送到 Telegram 群组或私聊中，让您能够实时收到邮件通知。支持自定义消息模板、多个聊天群组推送以及白名单过滤等功能。

相关配置位于 `localhost:3000/admin/system` 的 `电子邮件配置中`。


## 配置参数说明

### 必需参数

| 系统参数名（请勿修改） | 类型 | 说明 |
|--------|------|------|
| `enable_tg_email_push` | boolean | 是否启用 Telegram 推送功能 |
| `tg_email_bot_token` | string | Telegram Bot Token |
| `tg_email_chat_id` | string | 目标聊天 ID（支持多个，逗号分隔） |

### 可选参数

| 系统参数名（请勿修改） | 类型 | 说明 |
|--------|------|------|
| `tg_email_template` | string | 自定义消息模板 |
| `tg_email_target_white_list` | string | 邮件地址白名单（逗号分隔） |

## 第一步：创建 Telegram Bot

### 1. 与 BotFather 对话

1. 在 Telegram 中搜索 `@BotFather`
2. 发送 `/start` 开始对话
3. 发送 `/newbot` 创建新机器人

### 2. 设置机器人信息

1. 输入机器人的显示名称（例如：`Email Notifier`）
2. 输入机器人的用户名（必须以 `bot` 结尾，例如：`my_email_bot`）
3. BotFather 会返回一个 Token，格式类似：`123456789:ABCdefGHIjklMNOpqrSTUvwxyz`

## 第二步：获取聊天 ID

### 方法一：从网页版链接中获取

在浏览器中进入 Telegram 群组、频道，链接的最后一段数字就是 ID，例如：

`https://web.telegram.org/a/#-10043635341`

其中的 `-10043635341` 就是您的聊天 ID

### 方法二：私聊获取

1. 在 Telegram 中搜索您刚创建的机器人
2. 发送 `/start` 或任意消息给机器人
3. 打开浏览器，访问：

   ```bash
   https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates
   ```

   将 `<YOUR_BOT_TOKEN>` 替换为您的实际 Token
4. 在返回的 JSON 中找到 `"chat":{"id":123456789}` 部分，这个数字就是您的聊天 ID

### 方法三：群组获取

1. 将机器人添加到目标群组
2. 在群组中发送任意消息 @ 您的机器人（例如：`@my_email_bot hello`）
3. 访问上述 API 链接
4. 找到对应的群组聊天 ID（通常是负数，如：`-123456789`）

### 方法四：使用其他机器人

1. 搜索 `@userinfobot` 或 `@chatidbot`
2. 发送消息获取聊天 ID

## 第三步：系统配置

打开 `localhost:3000/admin/system` 页面，点击 `电子邮件配置`，填写对应的参数。

## 第四步：自定义消息模板

### 默认消息格式

如果不设置自定义模板，系统会使用以下默认格式：

```bash
📧 *New Email*

*From:* `发件人姓名 <邮箱地址>`
*To:* `收件人邮箱`
*Subject:* 邮件主题
*Date:* 发送日期

\`\`\`Content
邮件内容（前2000字符）
\`\`\`
```

### 自定义模板

您可以使用以下变量来自定义消息模板：

| 变量 | 说明 |
|------|------|
| `{{from}}` | 发件人信息 |
| `{{to}}` | 收件人邮箱 |
| `{{subject}}` | 邮件主题 |
| `{{text}}` | 邮件文本内容 |
| `{{date}}` | 发送日期 |

### 模板示例

```md
🔔 收到新邮件\n\n发件人：{{from}}\n主题：{{subject}}\n时间：{{date}}\n\n内容：\n{{text}}
```

## 功能特性

### 1. 多聊天群组支持
- 支持同时向多个 Telegram 群组或私聊发送通知
- 使用逗号分隔多个聊天 ID
- 发送失败不会影响其他群组的推送

### 2. 白名单过滤
- 支持设置收件人邮箱白名单
- 只有白名单中的邮箱收到邮件时才会推送到 Telegram
- 如果不设置白名单，则推送所有邮件

### 3. 内容处理
- 自动去除 HTML 标签
- 内容超过 2000 字符时自动截断
- 支持 Markdown 格式显示

### 4. 错误处理
- 自动重试机制
- 详细的错误日志记录
- 部分发送失败不影响整体流程

## 故障排除

### 常见问题

1. **机器人无法发送消息**
   - 检查 Bot Token 是否正确
   - 确认机器人已被添加到目标群组
   - 确认机器人在群组中有发送消息的权限

2. **获取不到聊天 ID**
   - 确保已经向机器人发送过消息
   - 检查 API 请求 URL 是否正确
   - 尝试使用专门的机器人获取 ID

3. **消息格式异常**
   - 检查自定义模板中的变量是否正确
   - 确认 Markdown 语法没有错误
   - 测试时可以先使用默认模板

4. **部分群组收不到消息**
   - 检查聊天 ID 是否正确
   - 确认机器人在对应群组中
   - 查看服务器日志中的错误信息
