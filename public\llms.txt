## WR.DO Overview

> Wr.do is a platform designed for developers, offering various tools and documentation aimed at simplifying web application development. The website provides resources on authentication, cloud integration, email handling, and more, establishing a comprehensive guide for users to effectively utilize its services. With examples and quick-start guides, it supports developers in implementing features quickly and efficiently.

- [WR.DO](https://wr.do/): Shorten links with analytics, manage emails and control subdomains—all on one platform.
- [Introduction – WR.DO](https://wr.do/docs): Welcome to the WR.DO documentation.

## Documentation
- [Authentification – WR.DO](https://wr.do/docs/developer/authentification): How to config the authentification.
- [Cloudflare Configs – WR.DO](https://wr.do/docs/developer/cloudflare): How to config the cloudflare api.
- [Cloudflare Email Worker Configs – WR.DO](https://wr.do/docs/developer/cloudflare-email-worker): How to config the cloudflare api.
- [Components – WR.DO](https://wr.do/docs/developer/components): Use React components in Markdown using MDX.
- [Config Files – WR.DO](https://wr.do/docs/developer/config-files): Make it your own with config files.
- [Database – WR.DO](https://wr.do/docs/developer/database): How to config your Neon database.
- [Email – WR.DO](https://wr.do/docs/developer/email): How to manage emails in this project.
- [Installation – WR.DO](https://wr.do/docs/developer/installation): How to install the project.
- [Markdown Files – WR.DO](https://wr.do/docs/developer/markdown-files): How works blog and docs.
- [Quick Start for Developer – WR.DO](https://wr.do/docs/developer/quick-start): Step by step installation
- [DNS Records – WR.DO](https://wr.do/docs/dns-records): Create and manage your DNS records.
- [Emails – WR.DO](https://wr.do/docs/emails): Create and manage your emails.
- [Cloudflare Pages & Workers Custom Domain – WR.DO](https://wr.do/docs/examples/cloudflare): Free parsing of Cloudflare Pages & Workers custom domain names.
- [Other Platforms – WR.DO](https://wr.do/docs/examples/other): Free parsing custom domain names.
- [Vercel Custom Domain – WR.DO](https://wr.do/docs/examples/vercel): Free parsing of Vercel custom domain names.
- [Zeabur Custom Domain – WR.DO](https://wr.do/docs/examples/zeabur): Free parsing of Zeabur custom domain names.
- [Open API – WR.DO](https://wr.do/docs/open-api): Some open APIs for WR.DO. It's free and unlimited to use!
- [Svg Icon API – WR.DO](https://wr.do/docs/open-api/icon): Simple way to generate your svg icon
- [Url to Markdown API – WR.DO](https://wr.do/docs/open-api/markdown): Extract website content and convert it to Markdown format
- [Meta Scraping API – WR.DO](https://wr.do/docs/open-api/meta-info): Extract website meta information
- [Qr Code API – WR.DO](https://wr.do/docs/open-api/qrcode): Generate customizable QR codes for any URL
- [Screenshot API – WR.DO](https://wr.do/docs/open-api/screenshot): Capture website screenshots
- [Url to Text API – WR.DO](https://wr.do/docs/open-api/text): Extract website content and convert it to Text format
- [Team Plan – WR.DO](https://wr.do/docs/plan): Three different plans to choose from
- [Quick Start Guide – WR.DO](https://wr.do/docs/quick-start): Get started with the WR.DO documentation.
- [Short URLs – WR.DO](https://wr.do/docs/short-urls): Create and manage your short URLs.
- [WRoom – WR.DO](https://wr.do/docs/wroom): A temporary, peer-to-peer, and secure chat room

## Pricing and Plans
- [Pricing Plans – WR.DO](https://wr.do/pricing): Three different plans to choose from
- [Team Plan – WR.DO](https://wr.do/docs/plan): Three different plans to choose from

## Privacy and Legal
- [Privacy – WR.DO](https://wr.do/privacy): The Privacy Policy for WR.DO.
- [Terms & Conditions – WR.DO](https://wr.do/terms): Read our terms and conditions.

## Login and Access
- [Login](https://wr.do/login): Login to your account
- [Login](https://wr.do/dashboard): Login to your account
- [Login](https://wr.do/dashboard/urls): Login to your account
- [Login](https://wr.do/dashboard/settings): Login to your account
- [Login](https://wr.do/dashboard/records): Login to your account
- [Login](https://wr.do/dashboard/scrape): Login to your account
- [Login](https://wr.do/emails): Login to your account

## Optional
- [https://wr.do/manifest.json](https://wr.do/manifest.json)
- [https://wr.do/robots.txt](https://wr.do/robots.txt)
- [https://wr.do/opengraph-image.jpg](https://wr.do/opengraph-image.jpg)
- [WRoom](https://wr.do/chat): A temporary, peer-to-peer, and secure chat room
- [Password Required](https://wr.do/password-prompt): Short link with password

