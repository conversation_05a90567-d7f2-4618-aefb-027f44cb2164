---
title: Deploy Guide
description: Choose your deployment method
---

<DocsLang en="/docs/developer/deploy" zh="/docs/developer/deploy-zh" />

<Callout type="info" twClass="mt-4">
  Before reading this document, it is recommended to first read [Quick Start](/docs/developer/quick-start), 
  to confirm that the necessary environment variables are ready.
</Callout>

## Deploy with Vercel (Recommended)

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/oiov/wr.do.git&project-name=wrdo)

Remember to fill in the necessary environment variables.

## Deploy with Docker Compose

<Callout type="warning" twClass="mt-4">
  Please create your database instance before deployment. 

  Set `SKIP_DB_CHECK` and `SKIP_DB_MIGRATION` to `false` in the `.env` file, this will start the database check and migration.
</Callout>

Create a new folder and copy the [docker-compose.yml](https://github.com/oiov/wr.do/blob/main/docker-compose.yml)、[.env](https://github.com/oiov/wr.do/blob/main/.env.example) file to the folder.

> Or only create a [docker-compose.yml](https://github.com/oiov/wr.do/blob/main/docker-compose.yml) file, just replace `${DATABASE_URL}` with your database connection url and so on.

```bash
- wrdo
  | - docker-compose.yml
  | - .env
```

Fill in the environment variables in the `.env` file, then: 

```bash
docker compose up -d
```

## Deploy with Docker Compose (Local DB)

Create a new folder and copy the `docker-compose-localdb.yml`、`.env` file to the folder.

```bash
- wrdo
  | - docker-compose.yml
  | - .env
```

Fill in the environment variables in the `.env` file, then: 

```bash
docker compose up -d
```

## Official Image

```bash
docker pull ghcr.io/oiov/wr.do/wrdo:latest
```

Find the official image here: [container/wr.do](https://github.com/oiov/wr.do/pkgs/container/wr.do%2Fwrdo)

## Build Image

Fork this repository and trigger the build image action in Actions.

## Paid Deployment Service

**Contact:** WeChat `oiovdev`

This project offers professional deployment services with flexible pricing based on different requirements.

### Deployment Plans & Pricing

| Deployment Method | Configuration | Service Fee |
| --- | --- | --- |
| Vercel | App hosting + Self-hosted database + Domain configuration | ¥500 |
| Vercel | App hosting + Neon cloud database + Domain configuration | ¥400 |
| Docker | Server deployment + Self-hosted database + Domain configuration | ¥500 |
| Docker | Server deployment + Neon cloud database + Domain configuration | ¥450 |

### Important Notes

- **Database Requirements:** Self-hosted database requires server preparation
- **Domain Requirements:** At least one domain required, must be managed through Cloudflare
- **Server & Domain Costs Not Included:** Server purchase and domain registration fees are not included in the above service fees. Please prepare them in advance
- **Default Environment Configuration:** Server deployment includes aaPanel (BaoTa) installation by default for management

> **Recommended Cloud Server:** RackNerd, US-based VPS (no ICP filing required), 2 cores 2GB configuration for only $20.98≈¥145/year, supports Alipay payment. [💁Click here for discount link](https://my.racknerd.com/aff.php?aff=10906&pid=681)

> **Recommended Domain Registrar:** [NameSilo](https://www.namesilo.com/?rid=50fae21ln), new users can save $1 with coupon code: **wrdo**

### Prerequisites

Please register accounts on the following platforms before deployment:

- **Cloudflare**: https://dash.cloudflare.com (Required, domain management service)
- **Resend**: https://resend.com (Required, email delivery service)
- **Vercel**: https://vercel.com (Optional, app deployment platform)
- **Neon**: https://neon.tech (Optional, cloud database service)

### Contact Information

For deployment services, please add WeChat `oiovdev` for detailed consultation. We will provide customized deployment solutions based on your specific needs.