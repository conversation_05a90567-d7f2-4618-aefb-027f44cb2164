---
title: Email
description: How to manage emails in this project.
---

<DocsLang en="/docs/developer/email" zh="/docs/developer/email-zh" />

In the WR.DO project, there are two features that rely on Resend, 
one is email login (magic link), and the other is email sending feature (if you need to receive email feature, 
please refer to /docs/developer/cloudflare-email-worker). 

The `RESEND_API_KEY` and `RESEND_SROM_SMAIL` configured in the `.env` file are used for login feature, 
while the Resend key required for email sending feature needs to be added by 
yourself in the domain configuration after logging into the admin panel (`/admin/domains`).

<Callout type="note">
  Two features can use the same key, as both essentially use Resend to send emails.
</Callout>

The following will demonstrate how to configure the Resend key required for login.

## Steps

<Callout type="note">
  The email part is similar at the [resend](https://resend.com/) documentation.
  You can find the official documentation
  [here](https://authjs.dev/getting-started/installation#setup-environment) if
  you want.
</Callout>

<Steps>

### Create an account

If don't have an account on Resend, just follow their steps after signup [here](https://resend.com/signup).

> Resend's free account offers a daily email limit of 100 emails, bound to 1 domain name, which is sufficient for ordinary users. 

### Create an API key

After signin on Resend, he propurse you to create your first API key.

Copy/paste in your `.env` file.

```js
RESEND_API_KEY = re_your_resend_api_key;
RESEND_FROM_EMAIL="you <<EMAIL>>"
```

</Steps>
