services:
  app:
    image: ghcr.io/oiov/wr.do/wrdo:${TAG:-main}
    container_name: wrdo
    ports:
      - "3000:3000"
    environment:
      NODE_ENV: production
      DATABASE_URL: ${DATABASE_URL}
      AUTH_SECRET: ${AUTH_SECRET:-your-auth-secret}
      AUTH_URL: ${AUTH_URL}
      NEXT_PUBLIC_APP_URL: ${NEXT_PUBLIC_APP_URL}
      GOOGLE_CLIENT_ID: ${GOOGLE_CLIENT_ID}
      GOOGLE_CLIENT_SECRET: ${GOOGLE_CLIENT_SECRET}
      GITHUB_ID: ${GITHUB_ID}
      GITHUB_SECRET: ${GITHUB_SECRET}
      LinuxDo_CLIENT_ID: ${LinuxDo_CLIENT_ID}
      LinuxDo_CLIENT_SECRET: ${LinuxDo_CLIENT_SECRET}
      RESEND_API_KEY: ${RESEND_API_KEY}
      RESEND_FROM_EMAIL: ${RESEND_FROM_EMAIL}
      NEXT_PUBLIC_EMAIL_R2_DOMAIN: ${NEXT_PUBLIC_EMAIL_R2_DOMAIN}
      NEXT_PUBLIC_GOOGLE_ID: ${NEXT_PUBLIC_GOOGLE_ID}
      SCREENSHOTONE_BASE_URL: ${SCREENSHOTONE_BASE_URL}
      GITHUB_TOKEN: ${GITHUB_TOKEN}
      SKIP_DB_CHECK: ${SKIP_DB_CHECK}
      SKIP_DB_MIGRATION: ${SKIP_DB_MIGRATION}
    networks:
      - wrdo-network
    restart: unless-stopped

networks:
  wrdo-network:
    driver: bridge
