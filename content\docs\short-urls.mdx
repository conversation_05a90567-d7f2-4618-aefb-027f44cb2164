---
title: Short URLs
description: Create and manage your short URLs.
---

<Callout type="warning" twClass="mt-0">
  Please do not abuse the free short link service. If any illegal or malicious activities are discovered, the account will be banned. If you need help, please contact us.
</Callout>

## Generating Short Links

1. **Navigate to Short Links Page**: Select the [Short Links](https://wr.do/dashboard/urls) option in the navigation bar.
2. **Input Long URL**: Paste or type the URL you want to shorten into the provided input box.
3. **Generate Short Link**: Click the "Generate" button, and the system will automatically create a short link for you.
4. **Copy and Use**: Click the "Copy" button to copy the short link to your clipboard, and you can use it wherever needed.

## URL Stats

- **Clicks**: The number of times the short link has been clicked.
- **Visitors**: The number of unique visitors who have visited the short link.

WR.DO provides a simple access statistics feature that can be used to track the click count and visitor count of the short link.

## Expiration

- **No Expiration**: The short link will never expire.
- **Expiration**: The short link will expire after the specified time.

Once the generated short chain becomes invalid, it will not be deleted. When accessing the short chain again, it will be redirected to this page. Users can reset the short chain validity period to activate.

## Password

- **No Password**: The short link will not require a password.
- **Password**: The short link will require a password to access.

Users can set a password for the short link to protect it from unauthorized access.

## API Reference

The `POST /api/v1/short` endpoint allows you to create a new short link for a given long URL.


```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -H "wrdo-api-key: YOUR_API_KEY" \
  -d '{
    "target": "https://www.oiov.dev",
    "url": "abc123",
    "expiration": "-1",
    "prefix": "wr.do",
    "visible": 1,
    "active": 1,
    "password": ""
  }' \
  https://wr.do/api/v1/short
```

### Request Body (Params)

```json
{
  "target": "https://www.oiov.dev", // required
  "url": "abc123", // required, slug
  "expiration": "-1", // optional, seconds, default: "-1", "-1" means no expiration; "60" means 60 seconds
  "prefix": "wr.do", // optional, default: wr.do
  "visible": 1, // optional, default: 1, 1: visible, 0: invisible
  "active": 1, // optional, default: 1, 1: active, 0: inactive
  "password": "" // optional, max length: 6
}
```

### Authorization Header

- `wrdo-api-key`: You can use your API key to authenticate your requests. 
You can find your API key in your [account settings](/dashboard/settings).
Add the header `wrdo-api-key: YOUR_API_KEY` to your request.

### Response

```json
{
  "id": "c_abcd123",
  "userId": "string",
  "userName": "string",
  "target": "https://www.example.com",
  "url": "abc123",
  "prefix": "wr.do",
  "visible": 1,
  "active": 1,
  "expiration": "-1",
  "password": "",
  "createdAt": 2025-01-01T00:00:00.000Z,
  "updatedAt": 2025-01-01T00:00:00.000Z
}
```

## Short Link Issues

### Expired Links

<img className='size-20 mx-auto mt-4' src="/_static/docs/link/expired.svg"/>

<Callout type="error" twClass="">
If you see this, it means that this short link has expired. 
Please contact the creator of this short link to reactivate, or create a new short link.
</Callout>

### Missing links

<img className='size-20 mx-auto mt-4' src="/_static/docs/link/error-404.svg"/>

<Callout type="error" twClass="">
If you see this, it means that this short link does not exist.
Please contact the creator of this short link to reactivate, or create a new short link.
</Callout>

### Disabled links

<img className='size-20 mx-auto mt-4' src="/_static/docs/link/disabled.svg"/>

<Callout type="error" twClass="">
If you see this, it means that this short link has been disabled.
Please contact the creator of this short link to reactivate, or create a new short link.
</Callout>

### Error links

<img className='size-20 mx-auto mt-4' src="/_static/docs/link/error.svg"/>

<Callout type="error" twClass="">
If you see this, it means that this short link has an error. Please contact the creator of this short link or administrator.
</Callout>

### Password Required

<img className='size-20 mx-auto mt-4' src="/_static/docs/link/password.svg"/>

<Callout type="error" twClass="">
This short link requires a password. Enter the correct password to access it.
</Callout>

### Incorrect Password

<img className='size-20 mx-auto mt-4' src="/_static/docs/link/password-error.svg"/>

<Callout type="error" twClass="">
The password provided is incorrect. Try again or contact the link creator for the correct password.
</Callout>