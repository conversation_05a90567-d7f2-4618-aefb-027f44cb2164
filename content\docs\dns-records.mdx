---
title: DNS Records
description: Create and manage your DNS records.
---

## Legitimacy review

<Callout type="danger" twClass="mb-3">
- To avoid abuse, applications without website content will be rejected
- To avoid domain name conflicts, please check before applying
- Completed website construction or released open source project (ready to build website for open source project)
- Political sensitivity, violence, pornography, link jumping, VPN, reverse proxy services, and other illegal or sensitive content must not appear on the website

**Administrators will conduct domain name checks periodically to clean up domain names that violate the above rules, have no content, and are not open source related**
</Callout>

## 合法性审查

<Callout type="danger" twClass="mb-3">
- 为了避免滥用，无网站内容的申请将被拒绝
- 为了避免域名冲突，请在申请前进行检查
- 已经完成网站建设或已经发布开源项目（准备为开源项目搭建网站）
- 不可在网站中出现政治敏感及暴力、色情、链接跳转、VPN、反向代理服务等违法或敏感内容

**管理员会不定期进行域名检查，对违反以上规则、无内容和非开源相关域名进行清理。**

特别提示：如果您的网站内容不符合中国大陆法律法规，您的域名将会被删除，且不会提供备份。
</Callout>

## What is a DNS Record?

A DNS record is a piece of information that is stored in a DNS zone. It is a record that contains a hostname and an IP address. A DNS record can be a CNAME or an A record.

## When to use DNS Records?

- When you need to redirect traffic from one domain to another.
- When you need to redirect traffic from one IP address to another.

## Why use WR.DO DNS Records?

WR.DO provide a **free DNS record** management service that can help you quickly create and manage DNS records. You do not need to purchase additional domains to use one short and resolve to your server.

## Creating DNS Records with WR.DO

1. **Navigate to DNS Management Page**: After logging in, you will see a "DNS Management" option in the navigation bar, click [DNS Management](https://wr.do/dashboard/records) to proceed.
2. **Add Record**: Click the "Add Record" button and select the type of record you need (such as CNAME, A, etc.).
3. **Fill in Details**: Enter the hostname, target address, and other details as prompted.
4. **Save Record**: After confirming everything is correct, click the "Save" button.

## Examples

- [Vercel custom domain](/docs/examples/vercel)
- [Zeabur custom domain](/docs/examples/zeabur)

WR.DO provides limited free domain name resolution. If you need more subdomain resolution, please contact the administrator email `<EMAIL>` .