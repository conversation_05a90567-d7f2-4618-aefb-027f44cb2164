{"Common": {"Set up an administrator": "Set up an administrator", "Add the first domain": "Add the first domain", "Congrats on completing setup 🎉": "Congrats on completing setup 🎉", "Admin Setup Guide": "Admin Setup Guide", "Previous": "Previous", "Next": "Next", "🚀 Start": "🚀 Start", "Ready": "Ready", "Allow Sign Up": "Allow Sign Up", "Set {email} as ADMIN": "Set {email} as ADMIN", "Active Now": "Active Now", "Only by becoming an administrator can one access the admin panel and add domain names": "Only by becoming an administrator can one access the admin panel and add domain names", "Administrators can set all user permissions, allocate quotas, view and edit all resources (short links, subdomains, email), etc": "Administrators can set all user permissions, allocate quotas, view and edit all resources (short links, subdomains, email), etc", "Via": "Via", "quick start": "quick start", "docs to get more information": "docs to get more information", "Domain Name": "Domain Name", "Please enter a valid domain name (must be hosted on Cloudflare)": "Please enter a valid domain name (must be hosted on Cloudflare)", "Or add later": "Or add later", "Submit": "Submit", "After v1-0-2, this setup guide is not needed anymore": "After v1.0.2, this setup guide is not needed anymore"}, "List": {"Short URLs": "Short URLs", "Manage Short URLs": "Manage Short URLs", "Add URL": "Add URL", "Slug": "Slug", "Target": "Target", "User": "User", "Enabled": "Enabled", "Expiration": "Expiration", "Clicks": "<PERSON>licks", "Updated": "Updated", "Actions": "Actions", "Edit": "Edit", "Search by slug": "Search by slug", "Search by target": "Search by target", "Search by username": "Search by username", "Create short link": "Create short link", "Edit short link": "Edit short link", "Delete": "Delete", "Target URL": "Target URL", "Required": "Required", "Short Link": "Short Link", "A random url suffix": "A random url suffix", "Final url like": "Final url like", "Password": "Password", "Optional": "Optional", "If you want to protect your link": "If you want to protect your link", "Enter 6 character password": "Enter 6 character password", "Expiration time, default for never": "Expiration time, default for never", "Save": "Save", "Cancel": "Cancel", "Update": "Update", "QR Code Design": "QR Code Design", "Preview": "Preview", "Download SVG": "Download SVG", "Download PNG": "Download PNG", "Download JPG": "Download JPG", "Url": "Url", "Logo": "Logo", "Custom Logo": "Custom Logo", "Front Color": "Front Color", "Background Color": "Background Color", "Display your logo in the center of the QR code": "Display your logo in the center of the QR code", "Learn more": "Learn more", "Customize your QR code logo": "Customize your QR code logo", "Please create a api key before use this feature": "Please create a api key before use this feature", "Learn more about": "Learn more about", "Create Api Key": "Create Api Key", "Show": "Show", "per page": "per page", "Total Subdomains": "Total Subdomains", "Subdomain List": "Subdomains", "Before using please read the": "Before using please read the", "Legitimacy review": "Legitimacy review", "See": "See", "examples": "examples", "for more usage": "for more usage", "Add Record": "Add Record", "Type": "Type", "Name": "Name", "Content": "Content", "TTL": "TTL", "Status": "Status", "Pending": "Pending", "The record is currently pending for admin approval": "The record is currently pending for admin approval", "The target is currently inaccessible": "The target is currently inaccessible", "Please check the target and try again": "Please check the target and try again", "If the target is not activated within 3 days": "If the target is not activated within 3 days", "the administrator will": "the administrator will", "delete this record": "delete this record", "Review": "Review", "No Subdomains": "No Subdomains", "No urls": "No urls", "Create record": "Create record", "Edit record": "Edit record", "The administrator has enabled application mode": "The administrator has enabled application mode", "After submission, you need to wait for administrator approval before the record takes effect": "After submission, you need to wait for administrator approval before the record takes effect", "What are you planning to use the subdomain for?": "What are you planning to use the subdomain for?", "At least 20 characters, Max 100 characters": "At least 20 characters, Max 100 characters", "User email": "User email", "Domain": "Domain", "No domains configured": "No domains configured", "Select a domain": "Select a domain", "IPv4 address": "IPv4 address", "Example": "E.g.", "Time To Live": "Time To Live", "Proxy": "Proxy", "Proxy status": "DNS response is replaced by Cloudflare Anycast IP", "Total Domains": "Total Domains", "Add Domain": "Add Domain", "Domain Name": "Domain Name", "Shorten Service": "Shorten Service", "Email Service": "Email Service", "Subdomain Service": "Subdomain Service", "Active": "Active", "Search by domain name": "Search by domain name", "No Domains": "No Domains", "Verified": "Verified", "Verify Configuration": "Verify Configuration", "Create Domain": "Create Domain", "Edit Domain": "Edit Domain", "Base": "Base", "Services": "Services", "Cloudflare Configs": "<PERSON><PERSON><PERSON><PERSON> Configs", "Zone ID": "Zone ID", "API Token": "API Token", "Account Email": "Account <PERSON><PERSON>", "How to get zone id?": "How to get zone id?", "How to get api key?": "How to get api key?", "How to get cloudflare account email?": "How to get cloudflare account email?", "Resend Configs": "Resend Configs", "Associate with 'Subdomain Service' status": "Associate with 'Subdomain Service' status", "Associate with 'Email Service' status": "Associate with 'Email Service' status", "API Key": "API Key", "send email service": "send email service", "How to get resend api key?": "How to get resend api key?", "Analytics": "Analytics", "Edit URL": "Edit URL", "Plan Name": "Plan", "Quota Settings": "<PERSON><PERSON><PERSON>", "Short Limit": "Short Limit", "Record Limit": "Record Limit", "Email Limit": "<PERSON><PERSON>", "Send Limit": "Send Limit", "Domain Limit": "Domain Limit", "Add Plan": "Add Plan", "No Plans": "No Plans", "Create Plan": "Create Plan", "Edit Plan": "Edit Plan", "Monthly limit of short links created": "Monthly limit of short links created", "Monthly limit of subdomains created": "Monthly limit of subdomains created", "Monthly limit of emails sent": "Monthly limit of emails sent", "Monthly limit of email addresses created": "Monthly limit of email addresses created", "Reject": "Reject", "Rejected": "Rejected", "View Period": "View Period", "Time range for viewing short link visitor statistics data (days)": "Time range for viewing short link visitor statistics data (days)", "Tracked Limit": "Tracked Limit", "Monthly limit of tracked clicks (times)": "Monthly limit of tracked clicks (times)", "Limit on the number of allowed domains": "Limit on the number of allowed domains", "Only active plans can be used": "Only active plans can be used", "Plan name must be unique": "Plan name must be unique", "Record Types": "Record Types", "Allowed record types": "Allowed record types", "use `,` to separate": "use `,` to separate", "Agree": "Agree", "Min URL Length": "Min URL Length", "Min Email Length": "<PERSON>", "Min Subdomain Length": "Min Subdomain Length", "Limit Configs": "Limit Configs", "Email": "Email", "Role": "Role", "Plan": "Plan", "Join": "Join", "ADMIN": "ADMIN", "Admin": "Admin", "USER": "USER", "Total Users": "Total Users", "Edit User": "Edit User", "Login Password": "Password", "Duplicate": "Duplicate", "Confirm duplicate domain": "Confirm duplicate domain", "This will duplicate all configuration information for the {domain} domain, and create a new domain": "This will duplicate all configuration information for the {domain} domain, and create a new domain", "Add User": "Add User", "Loading storage buckets": "Loading storage buckets", "No buckets found": "No buckets found", "The administrator has not configured the storage bucket, no file can be uploaded": "The administrator has not configured the storage bucket, no file can be uploaded", "No Files": "No Files", "You don't upload any files yet": "You don't upload any files yet", "Size": "Size", "Date": "Date", "Download": "Download", "Generate short link": "Shorten link", "Delete File": "Delete", "Select all": "Select all", "Delete selected": "Delete selected", "Update short link": "Update short link", "QR Code": "QR Code", "Raw Data": "Raw Data", "Storage Service": "Storage Service", "Max File Size": "<PERSON> Si<PERSON>", "Maximum uploaded single file size in bytes": "Maximum uploaded single file size in bytes", "Max Total Size": "Max Total Size", "Maximum uploaded total file size in bytes": "Maximum uploaded total file size in bytes", "storageUsage": "Storage Usage", "used": "Used", "usedSpace": "Used Space", "totalCapacity": "Total Capacity", "availableSpace": "Available Space", "storageFull": "Storage space is almost full", "storageHigh": "Storage space usage is high", "storageGood": "Storage space is sufficient", "items": "items", "Total": "Total", "Configuration Error": "Configuration Error"}, "Components": {"Dashboard": "Dashboard", "Live Logs": "Live Logs", "Real-time logs of short link visits": "Real-time logs of short link visits", "Stop": "Stop", "Live": "Live", "Time": "Time", "Slug": "Slug", "Target": "Target", "Location": "Location", "Clicks": "<PERSON>licks", "of": "/", "total logs": "total logs", "API Reference": "API Reference", "provide a api for {target}": "provide a api for {target}", "creating short urls": "creating short urls", "View the usage tutorial": "View the usage tutorial", "document": "document", "Realtime Visits": "Realtime Visits", "See documentation": "See documentation", "Manage Short URLs": "Manage Short URLs", "List and manage short urls": "List and manage short urls", "Manage DNS Records": "Manage DNS Records", "List and manage records": "List and manage records", "Scraping API Overview": "Scraping API Overview", "Quickly extract valuable structured website data": "Quickly extract valuable structured website data", "Url to Screenshot": "Url to Screenshot", "Quickly extract website screenshots": "Quickly extract website screenshots", "extracting url as screenshot": "extracting url as screenshot", "Url to QR Code": "Url to QR Code", "Quickly extract website QR codes": "Quickly extract website QR codes", "extracting url as qrcode": "extracting url as qrcode", "Url to Meta Info": "Url to Meta Info", "extracting url as meta info": "extracting url as meta info", "Url to Markdown": "Url to <PERSON>down", "Quickly extract website content and convert it to Markdown format": "Quickly extract website content and convert it to Markdown format", "extracting url as markdown": "extracting url as markdown", "extracting url as text": "extracting url as text", "Account Settings": "Account <PERSON><PERSON>", "Manage account and website settings": "Manage account and website settings", "Setup Guide": "Setup Guide", "Admin Panel": "Admin Panel", "Access only for users with ADMIN role": "Access only for users with ADMIN role", "Domains Management": "Domains Management", "List and manage domains": "List and manage domains", "User Management": "User Management", "List and manage all users": "List and manage all users", "Email box": "Email box", "monthly": "monthly", "total": "total", "Short URLs": "Short URLs", "DNS Records": "DNS Records", "Url to Text": "Url to Text", "Take a screenshot of the webpage": "Take a screenshot of the webpage", "Extract website metadata": "Extract website metadata", "Convert website content to Markdown format": "Convert website content to Markdown format", "Convert website content to text": "Convert website content to text", "Emails": "Emails", "Inbox": "Inbox", "Users": "Users", "Total Requests of APIs in Last 30 Days": "Total Requests of APIs in Last 30 Days", "Last request from {latestFrom} api about {latestDate}": "Last request from {latestFrom} api about {latestDate}", "last-request-info": "Last request from {location} about <timeAgo></timeAgo>", "Requests": "Requests", "IP": "IP", "Date": "Date", "Type": "Type", "Link": "Link", "User": "User", "Data Increase": "Data Increase", "Showing data increase in": "Showing data increase in", "Records": "Records", "URLs": "URLs", "Sends": "Sends", "Link Analytics": "Link Analytics", "Last visitor from {latestFrom} about {latestDate}": "Last visitor from {latestFrom} about {latestDate}", "last-visitor-info": "Last visitor from {location} about <timeAgo></timeAgo>", "Views": "Views", "Visits": "Visits", "Referrers": "Referrers", "Traffic Type": "Traffic Type", "Country": "Country", "City": "City", "Browser": "Browser", "Engine": "Engine", "Language": "Language", "Region": "Region", "Device": "<PERSON><PERSON>", "OS": "OS", "CPU": "CPU", "Visitors": "Visitors", "Name": "Name", "Activated Api Key users": "Activated Api Key users", "total-requests-one-type": "Total requests of {type1}", "total-requests-two-types": "Total requests of {type1} and {type2}", "Protected Link": "Protected Link", "You are attempting to access a password-protected link": "You are attempting to access a password-protected link", "Please contact the owner to get the password": "Please contact the owner to get the password", "Learn more about this from our": "Learn more about this from our", "docs": "docs", "Incorrect password": "Incorrect password", "Please try again": "Please try again", "Unlock": "Unlock", "Unlocking": "Unlocking...", "Last 24 Hours": "Last 24 Hours", "Last 7 Days": "Last 7 Days", "Last 30 Days": "Last 30 Days", "Last 2 Months": "Last 2 Months", "Last 3 Months": "Last 3 Months", "Last 6 Months": "Last 6 Months", "Last 1 Year": "Last 1 Year", "All the time": "All the time", "No Visits": "No Visits", "You don't have any visits yet in": "You don't have any visits yet in", "System Settings": "System Settings", "Active": "Active", "Inactive": "Inactive", "Pending": "Pending", "Rejected": "Rejected", "linkNotExist": "Link Not Found", "linkNotExistDescription": "Sorry, the short link you are trying to access does not exist. It may have been deleted or never created.", "linkExpired": "Link Expired", "linkExpiredDescription": "This short link has expired and can no longer be used.", "linkDisabled": "<PERSON> Disabled", "linkDisabledDescription": "This short link has been disabled by its creator.", "systemError": "System Error", "systemErrorDescription": "An error occurred while processing your request. Please try again later.", "contactCreatorReactivate": "Please contact the creator of this short link to reactivate or create a new short link", "contactCreatorOrAdmin": "Please contact the creator of this short link or system administrator", "shortLink": "Short Link", "retry": "Retry", "backToHome": "Back to Home", "goBack": "Go Back", "contactSupportIfError": "If you believe this is an error, please contact technical support", "Actived": "Active", "Disabled": "Disabled", "Expired": "Expired", "PasswordProtected": "Password Protected", "Upload List": "Upload List", "Uploading": "Uploading", "Completed": "Completed", "Aborted": "Aborted", "Drop files to upload them to": "Drop files to upload them to", "Drag and drop file(s) here": "Drag and drop file(s) here", "or": "or", "Browse file(s)": "Browse file(s)", "Cloud Storage": "Cloud Storage", "List and manage cloud storage": "List and manage cloud storage", "Cancel": "Cancel", "Clear": "Clear", "Upload Files": "Upload Files", "Uploud channel": "Uploud channel", "Do not close the window until the upload is complete": "Do not close the window until the upload is complete", "Pending Upload": "Pending Upload", "Start Upload": "Start Upload", "Limit": "Limit", "Files pasted successfully": "Files pasted successfully"}, "Landing": {"settings": "Settings", "Dashboard": "Dashboard", "deployWithVercel": "Deploy with", "now": "now!", "onePlatformPowers": "One platform powers", "endlessSolutions": "endless solutions", "platformDescription": "Link shortening, domain hosting, email manager and open api, everything you need to build better.", "documents": "Documents", "signInForFree": "Sign in for free", "exampleImageAlt": "example", "urlShorteningTitle": "URL Shortening", "urlShorteningDescription": "📖 Instantly transform long, unwieldy URLs into short, memorable links that are easy to share. Enjoy built-in analytics to track clicks, monitor performance, and gain insights into your audience—all in real time.", "freeSubdomainHostingTitle": "Free Subdomain Hosting", "freeSubdomainHostingDescription": "🎉 Kickstart your online presence with free, fully customizable subdomains. Whether you're launching a personal project or testing a business idea, get started quickly with no cost and reliable hosting you can trust.", "emailReceiversSendersTitle": "Email Receivers & Senders", "emailReceiversSendersDescription": "📧 Seamlessly receive and send emails from any email provider with top-notch security. Stay connected and manage your communications effortlessly, knowing your data is protected with robust encryption and privacy features.", "multipleDomainsTitle": "Multiple Domains", "multipleDomainsDescription": "🤩 Empower your business with the flexibility of multiple domains, such as wr.do, uv.do, and more. Establish a strong digital footprint, create branded links, or manage diverse projects—all under one unified platform.", "websiteScreenshotApiTitle": "Website Screenshot API", "websiteScreenshotApiDescription": "📷 Capture high-quality screenshots of any webpage instantly with our powerful Screenshot API. Integrate seamlessly into your applications, access third-party services, and unlock advanced features by applying your unique API key.", "metaInformationApiTitle": "Meta Information API", "metaInformationApiDescription": "🍥 Extract rich, structured web data effortlessly with our smart Meta Information API. Perfect for developers, businesses, or researchers, this tool offers seamless integration, third-party service access, and enhanced functionality.", "applyYourApiKey": "Apply your api key", "pricingTitle": "Pricing", "pricingDescription": "Use it for free for yourself, upgrade when your team needs advanced control.", "freeTier": "Free", "freePrice": "$0/mo", "freeBestFor": "For hobbyists and individuals looking to manage their links", "getStartedFree": "Get started free", "premiumTier": "Premium", "premiumPrice": "$5/mo", "premiumBestFor": "Best for 5-50 users", "getFreeTrial": "Get free trial", "enterpriseTier": "Enterprise", "enterprisePrice": "Contact us", "enterpriseBestFor": "For large organizations with custom needs", "contactUs": "Contact us"}, "Auth": {"Back": "Back", "Welcome to": "Welcome to", "Choose your login method to continue": "Choose your login method to continue", "By clicking continue, you agree to our": "By clicking continue, you agree to our", "Terms of Service": "Terms of Service", "and": "and", "Privacy Policy": "Privacy Policy", "Or continue with": "Or continue with", "Email": "Email", "Sign Up with Email": "Sign Up with Email", "Sign In with Email": "Sign In with <PERSON><PERSON>", "Email Code": "Email", "Password": "Password", "Sign In / Sign Up": "Sign In / Sign Up", "Incorrect email or password": "Incorrect email or password, or administrator closed new user registration", "Something went wrong": "Something went wrong", "Check your email": "Check your email", "We sent you a login link": "We sent you a login link", "Be sure to check your spam too": "Be sure to check your spam too", "Welcome back!": "Welcome back!", "Unregistered users will automatically create an account": "Unregistered users will automatically create an account", "Administrator has disabled new user registration": "Administrator has disabled new user registration", "Email domain not supported, Please use one of the following:": "Email domain not supported. Please use one of the following:", "Auth configuration error": "Auth configuration error", "Unknown error": "Unknown error"}, "System": {"MENU": "<PERSON><PERSON>", "Dashboard": "Dashboard", "Short Urls": "Short Urls", "Emails": "Emails", "DNS Records": "DNS Records", "WRoom": "WRoom", "OPEN API": "OPEN API", "Overview": "Overview", "Screenshot": "Screenshot", "QR Code": "QR Code", "Meta Info": "Meta Info", "Markdown": "<PERSON><PERSON>", "ADMIN": "ADMIN", "Admin Panel": "Admin Panel", "Domains": "Domains", "Users": "Users", "URLs": "URLs", "Records": "Records", "OPTIONS": "OPTIONS", "Settings": "Settings", "Documentation": "Documentation", "Feedback": "<PERSON><PERSON><PERSON>", "Support": "Support", "Docs": "Docs", "Toggle theme": "Toggle theme", "Light": "Light", "Dark": "Dark", "System": "System", "Admin": "Admin", "Sign in": "Sign in", "Log out": "Log out", "System Settings": "System Settings", "Cloud Storage": "Cloud Storage", "Cloud Storage Manager": "Cloud Storage"}, "Email": {"Search emails": "Search emails", "Create New Email": "Create New Email", "Email Address": "Email Address", "Inbox Emails": "Inbox Emails", "Unread Emails": "Unread Emails", "Filter unread emails": "Filter unread emails", "Sent Emails": "Sent Em<PERSON>", "Admin Mode": "Admin Mode", "No emails": "No emails", "{email} recived": "{email} recived", "Edit email": "Edit email", "Create new email": "Create new email", "Enter email prefix": "Enter email prefix", "No domains configured": "No domains configured", "Cancel": "Cancel", "Update": "Update", "Create": "Create", "Failed to load emails": "Failed to load emails", "Please try again": "Please try again", "No emails found": "No emails found", "Search by send to email": "Search by send to email", "Delete email": "Delete email", "You are about to delete the following email, once deleted, it cannot be recovered": "You are about to delete the following email, once deleted, it cannot be recovered", "All emails in inbox will be deleted at the same time": "All emails in inbox will be deleted at the same time", "Are you sure you want to continue?": "Are you sure you want to continue?", "To confirm, please type": "To confirm, please type", "delete": "delete", "below": "below", "Confirm Delete": "Confirm Delete", "INBOX": "INBOX", "Auto refresh": "Auto refresh", "more": "more", "Select all": "Select all", "Mask as read": "Mask as read", "Delete selected": "Delete selected", "Waiting for emails": "Waiting for emails", "No Email Address Selected": "No Email Address Selected", "Please select an email address from the list to view your inbox": "Please select an email address from the list to view your inbox", "Once selected, your emails will appear here automatically": "Once selected, your emails will appear here automatically", "How to use email to send or receive emails?": "How to use email to send or receive emails?", "Will my email or inbox expire?": "Will my email or inbox expire?", "What is the limit? It's free?": "What is the limit? It's free?", "How to create emails with api?": "How to create emails with api?", "Send Email": "Send Email", "From": "From", "To": "To", "Subject": "Subject", "Content": "Content", "Send": "Send", "Sending": "Sending", "Reply-To": "Reply-To", "Attachments": "Attachments", "Date": "Date"}, "Scrape": {"Playground": "Playground", "Automate your website screenshots and turn them into stunning visuals for your applications": "Automate your website screenshots and turn them into stunning visuals for your applications", "Start": "Start", "Scraping": "Scraping", "Scrape the meta data of a website": "Scrape the meta data of a website", "Text": "Text"}, "Setting": {"Name": "Name", "Your Name": "Your Name", "Save": "Save", "Please enter a display name you are comfortable with": "Please enter a display name you are comfortable with", "Max 32 characters": "Max 32 characters", "Your Role": "Your Role", "Role": "Role", "ADMIN": "ADMIN", "USER": "USER", "Select the role what you want for this app": "Select the role what you want for this app", "API Key": "API Key", "Generate": "Generate", "Generate a new API key to access the open apis": "Generate a new API key to access the open apis", "Delete Account": "Delete Account", "This is a danger zone - Be careful !": "This is a danger zone - Be careful !", "Are you sure": "Are you sure", "Active Subscription": "Active Subscription", "Permanently delete your {name} account": "Permanently delete your {name} account", " and your subscription": " and your subscription", "This action cannot be undone - please proceed with caution": "This action cannot be undone - please proceed with caution", "Warning": "Warning", "This will permanently delete your account and your active subscription!": "This will permanently delete your account and your active subscription!", "verification": "verification", "App Configs": "App Configs", "User Registration": "User Registration", "Allow users to sign up": "Allow users to sign up", "Subdomain Apply Mode": "Subdomain Apply Mode", "Enable subdomain apply mode, each submission requires administrator review": "Enable subdomain apply mode, each submission requires administrator review", "Notification": "系统通知", "Set system notification, this will be displayed in the header": "Set system notification, this will be displayed in the header", "Login Methods": "Login Methods", "Select the login methods that users can use to log in": "Select the login methods that users can use to log in", "Resend Email": "<PERSON><PERSON><PERSON>", "Email Password": "Email Password", "Your Password": "Your Password", "Update your password": "Update your password", "At least 6 characters, Max 32 characters": "At least 6 characters, <PERSON> 32 characters", "Subdomain Configs": "Subdomain Configs", "Email Configs": "<PERSON><PERSON>figs", "Enable email catch-all, all user's email address which created on this platform will be redirected to the catch-all email address": "Enable email catch all, user can use email address which created on this platform to register", "Catch-All Email Address": "Catch-All Email Address", "Set catch-all email address, split by comma if more than one, such as: 1@a-com,2@b-com, Only works when email catch all is enabled": "Set catch-all email address, split by comma if more than one, such as: <EMAIL>,<EMAIL>, Only works when email catch all is enabled", "Message Pusher": "Message Pusher", "Push message to Telegram groups": "Push message to Telegram groups", "Update now": "Update now", "Dismiss": "<PERSON><PERSON><PERSON>", "New version available": "New version available", "Check for updates": "Check for updates", "Telegram Pusher": "<PERSON><PERSON><PERSON>", "Telegram Bot Token": "Telegram Bot Token", "Telegram Group ID": "Telegram Group ID", "Telegram Message Template": "Telegram Message Template", "Telegram Push Email White List": "Telegram Push Email White List", "Set Telegram bot token, Only works when Telegram pusher is enabled": "Set Telegram bot token, Only works when Telegram pusher is enabled", "Set Telegram group ID, split by comma if more than one, such as: -10054275724,-10045343642": "Set Telegram group ID, split by comma if more than one, such as: -10054275724,-10045343642", "Set Telegram email message template": "设置 Telegram 邮件消息模板", "Set Telegram push email white list, split by comma, if not set, will push all emails": "Set Telegram push email white list, split by comma, if not set, will push all emails", "How to configure Telegram bot": "How to configure Telegram bot", "Email Suffix Limit": "Email Suffix Limit", "Enable eamil suffix limit, only works for resend email login and email password login methods": "Enable eamil suffix limit, only works for resend email login and email password login methods", "Need to configure": "Need to configure", "Email Suffix White List": "Email Suffix White List", "Set email suffix white list, split by comma, such as: gmail-com,yahoo-com,hotmail-com": "Set email suffix white list, split by comma, such as: gmail.com,yahoo.com,hotmail.com", "Application Status Email Notifications": "Application Status Email Notifications", "Send email notifications for subdomain application status updates; Notifies administrators when users submit applications and notifies users of approval results; Only available when subdomain application mode is enabled": "Send email notifications for subdomain application status updates; Notifies administrators when users submit applications and notifies users of approval results; Only available when subdomain application mode is enabled", "Cloud Storage Configs": "Cloud Storage Configs", "Verified": "Verified", "Verify Configuration": "Verify Configuration", "Clear": "Clear", "How to get the R2 credentials?": "How to get the R2 credentials?", "Endpoint": "Endpoint", "Access Key ID": "Access Key ID", "Secret Access Key": "Secret Access Key", "Enabled": "Enabled", "Bucket": "Bucket", "Bucket Name": "Bucket Name", "Public Domain": "Public Domain", "Max File Size": "<PERSON> Si<PERSON>", "Region": "Region", "Prefix": "Prefix", "Optional": "Optional", "Allowed File Types": "Allowed File Types", "Public": "Public", "Publicize this storage bucket, all registered users can upload files to this storage bucket; If not public, only administrators can upload files to this storage bucket": "Publicize this storage bucket, all registered users can upload files to this storage bucket; If not public, only administrators can upload files to this storage bucket", "Provider": "Provider", "How to get the S3 credentials?": "How to get the S3 credentials?", "Provider Unique Name": "Provider Unique Name", "Unique": "Unique", "Add Provider": "Add Provider", "{length} Buckets": "{length} Buckets", "Save Modifications": "Save Modifications"}}